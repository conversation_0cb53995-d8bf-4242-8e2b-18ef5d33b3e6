import { NextRequest, NextResponse } from 'next/server';
import db from '@/lib/database';
import { verifyToken } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const token = request.cookies.get('token')?.value;
    if (!token) {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 401 });
    }

    const decoded = verifyToken(token);
    if (!decoded || decoded.role !== 'admin') {
      return NextResponse.json({ error: 'Accès refusé' }, { status: 403 });
    }

    const { vendor_id, duration_days } = await request.json();

    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(startDate.getDate() + duration_days);

    db.prepare(`
      UPDATE users 
      SET subscription_start = ?, subscription_end = ?, is_active = 1
      WHERE id = ?
    `).run(
      startDate.toISOString().split('T')[0],
      endDate.toISOString().split('T')[0],
      vendor_id
    );

    return NextResponse.json({ message: 'Abonnement activé avec succès' });
  } catch (error) {
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 });
  }
}