import { NextRequest, NextResponse } from 'next/server';
import db from '@/lib/database';
import { verifyToken } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('token')?.value;
    if (!token) {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 401 });
    }

    const decoded = verifyToken(token);
    if (!decoded || decoded.role !== 'admin') {
      return NextResponse.json({ error: 'Accès refusé' }, { status: 403 });
    }

    const vendors = db.prepare(`
      SELECT 
        u.*,
        COUNT(o.id) as total_orders,
        SUM(o.amount) as total_revenue
      FROM users u
      LEFT JOIN orders o ON u.id = o.vendor_id
      GROUP BY u.id
      ORDER BY u.created_at DESC
    `).all();

    return NextResponse.json(vendors);
  } catch (error) {
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 });
  }
}