import { NextRequest, NextResponse } from 'next/server';
import db from '@/lib/database';
import { hashPassword, generateUniqueLink } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const { email, password, name } = await request.json();

    // Check if user already exists
    const existingUser = db.prepare('SELECT id FROM users WHERE email = ?').get(email);
    if (existingUser) {
      return NextResponse.json({ error: 'Un compte avec cet email existe déjà' }, { status: 400 });
    }

    const hashedPassword = hashPassword(password);
    const uniqueLink = generateUniqueLink();

    const result = db.prepare(`
      INSERT INTO users (email, password, name, unique_link)
      VALUES (?, ?, ?, ?)
    `).run(email, hashedPassword, name, uniqueLink);

    return NextResponse.json({ 
      message: 'Compte créé avec succès',
      userId: result.lastInsertRowid
    });
  } catch (error) {
    return NextResponse.json({ error: 'Erreur lors de la création du compte' }, { status: 500 });
  }
}