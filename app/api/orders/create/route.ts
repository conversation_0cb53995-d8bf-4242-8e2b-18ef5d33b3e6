import { NextRequest, NextResponse } from 'next/server';
import db from '@/lib/database';
import { generateOrderNumber } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const { 
      vendor_link, 
      customer_name, 
      customer_phone, 
      delivery_address, 
      product_code, 
      amount, 
      social_network 
    } = await request.json();

    // Find vendor by unique link
    const vendor = db.prepare('SELECT id, is_active FROM users WHERE unique_link = ?').get(vendor_link);
    if (!vendor) {
      return NextResponse.json({ error: 'Lien vendeur invalide' }, { status: 404 });
    }

    if (!vendor.is_active) {
      return NextResponse.json({ error: 'Le vendeur n\'est pas actif' }, { status: 403 });
    }

    const orderNumber = generateOrderNumber();

    const result = db.prepare(`
      INSERT INTO orders (
        order_number, vendor_id, customer_name, customer_phone, 
        delivery_address, product_code, amount, social_network
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      orderNumber, vendor.id, customer_name, customer_phone,
      delivery_address, product_code, amount, social_network
    );

    return NextResponse.json({ 
      message: 'Commande créée avec succès',
      order_number: orderNumber
    });
  } catch (error) {
    return NextResponse.json({ error: 'Erreur lors de la création de la commande' }, { status: 500 });
  }
}