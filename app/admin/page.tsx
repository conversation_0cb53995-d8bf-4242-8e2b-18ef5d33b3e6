'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Shield, Users, TrendingUp, LogOut, Calendar, CheckCircle, XCircle } from 'lucide-react';

interface Vendor {
  id: number;
  name: string;
  email: string;
  mvola_number?: string;
  subscription_start?: string;
  subscription_end?: string;
  is_active: boolean;
  total_orders: number;
  total_revenue: number;
  created_at: string;
}

export default function AdminPage() {
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    fetchVendors();
  }, []);

  const fetchVendors = async () => {
    try {
      const response = await fetch('/api/admin/vendors');
      if (response.ok) {
        const vendorsData = await response.json();
        setVendors(vendorsData);
      } else {
        router.push('/login');
      }
    } catch (error) {
      console.error('Error fetching vendors:', error);
    } finally {
      setLoading(false);
    }
  };

  const activateSubscription = async (vendorId: number, days: number = 30) => {
    try {
      const response = await fetch('/api/admin/subscription', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ vendor_id: vendorId, duration_days: days }),
      });

      if (response.ok) {
        await fetchVendors();
        alert('Abonnement activé avec succès');
      }
    } catch (error) {
      console.error('Error activating subscription:', error);
    }
  };

  const logout = async () => {
    await fetch('/api/auth/logout', { method: 'POST' });
    router.push('/login');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Shield className="h-12 w-12 text-blue-600 mx-auto mb-4 animate-pulse" />
          <p className="text-gray-600">Chargement...</p>
        </div>
      </div>
    );
  }

  const activeVendors = vendors.filter(v => v.is_active && v.subscription_end && new Date(v.subscription_end) > new Date());
  const totalRevenue = vendors.reduce((sum, v) => sum + (v.total_revenue || 0), 0);
  const totalOrders = vendors.reduce((sum, v) => sum + (v.total_orders || 0), 0);

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">Administration Live Pay Mada</h1>
            </div>
            <Button variant="outline" size="sm" onClick={logout}>
              <LogOut className="h-4 w-4 mr-2" />
              Déconnexion
            </Button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Vendeurs</p>
                  <p className="text-2xl font-bold text-gray-900">{vendors.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Vendeurs Actifs</p>
                  <p className="text-2xl font-bold text-gray-900">{activeVendors.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Commandes</p>
                  <p className="text-2xl font-bold text-gray-900">{totalOrders}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Chiffre d'Affaires</p>
                  <p className="text-2xl font-bold text-gray-900">{totalRevenue.toLocaleString()} Ar</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Vendors Table */}
        <Card>
          <CardHeader>
            <CardTitle>Gestion des Vendeurs</CardTitle>
            <CardDescription>
              Liste de tous les vendeurs inscrits sur la plateforme
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Vendeur</TableHead>
                    <TableHead>MVola</TableHead>
                    <TableHead>Statut</TableHead>
                    <TableHead>Abonnement</TableHead>
                    <TableHead>Commandes</TableHead>
                    <TableHead>CA</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {vendors.map((vendor) => {
                    const isActive = vendor.is_active && vendor.subscription_end && new Date(vendor.subscription_end) > new Date();
                    
                    return (
                      <TableRow key={vendor.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{vendor.name}</p>
                            <p className="text-sm text-gray-500">{vendor.email}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          {vendor.mvola_number || (
                            <span className="text-gray-400">Non configuré</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            {isActive ? (
                              <>
                                <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                                <span className="text-green-800 text-sm font-medium">Actif</span>
                              </>
                            ) : (
                              <>
                                <XCircle className="h-4 w-4 text-red-600 mr-2" />
                                <span className="text-red-800 text-sm font-medium">Inactif</span>
                              </>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {vendor.subscription_end ? (
                            <div className="text-sm">
                              <p>Expire le:</p>
                              <p className="font-medium">
                                {new Date(vendor.subscription_end).toLocaleDateString('fr-FR')}
                              </p>
                            </div>
                          ) : (
                            <span className="text-gray-400">Aucun</span>
                          )}
                        </TableCell>
                        <TableCell className="text-center font-medium">
                          {vendor.total_orders || 0}
                        </TableCell>
                        <TableCell className="font-medium">
                          {(vendor.total_revenue || 0).toLocaleString()} Ar
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => activateSubscription(vendor.id, 30)}
                            >
                              <Calendar className="h-4 w-4 mr-1" />
                              30j
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => activateSubscription(vendor.id, 7)}
                            >
                              <Calendar className="h-4 w-4 mr-1" />
                              7j
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}