'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Smartphone, CheckCircle, AlertCircle } from 'lucide-react';

export default function OrderPage() {
  const params = useParams();
  const vendorLink = params.link as string;
  
  const [formData, setFormData] = useState({
    customer_name: '',
    customer_phone: '',
    delivery_address: '',
    product_code: '',
    amount: '',
    social_network: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [orderNumber, setOrderNumber] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/orders/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          vendor_link: vendorLink,
          ...formData,
          amount: parseFloat(formData.amount)
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setOrderNumber(data.order_number);
        setSubmitted(true);
      } else {
        setError(data.error);
      }
    } catch (error) {
      setError('Erreur lors de la création de la commande');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  if (submitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md shadow-xl border-0">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="flex items-center justify-center mb-4">
                <div className="bg-green-100 p-3 rounded-full">
                  <CheckCircle className="h-12 w-12 text-green-600" />
                </div>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Commande confirmée !</h2>
              <p className="text-gray-600 mb-4">
                Merci pour votre commande. Votre numéro de commande est :
              </p>
              <div className="bg-gray-50 p-4 rounded-lg mb-6">
                <p className="text-3xl font-bold text-blue-600">#{orderNumber}</p>
              </div>
              <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Prochaine étape :</strong> Effectuez le paiement via MVola au numéro communiqué par le vendeur.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-gradient-to-r from-blue-600 to-green-600 p-3 rounded-full">
              <Smartphone className="h-8 w-8 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Nouvelle Commande</h1>
          <p className="text-gray-600">Remplissez le formulaire pour commander</p>
        </div>

        <Card className="shadow-xl border-0">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl text-center">Formulaire de commande</CardTitle>
            <CardDescription className="text-center">
              Toutes les informations sont requises
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="customer_name">Nom et prénom</Label>
                <Input
                  id="customer_name"
                  name="customer_name"
                  type="text"
                  value={formData.customer_name}
                  onChange={handleChange}
                  required
                  className="h-11"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="customer_phone">Numéro de téléphone</Label>
                <Input
                  id="customer_phone"
                  name="customer_phone"
                  type="tel"
                  value={formData.customer_phone}
                  onChange={handleChange}
                  placeholder="034 XX XXX XX"
                  required
                  className="h-11"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="delivery_address">Lieu de livraison</Label>
                <Input
                  id="delivery_address"
                  name="delivery_address"
                  type="text"
                  value={formData.delivery_address}
                  onChange={handleChange}
                  placeholder="Adresse complète"
                  required
                  className="h-11"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="product_code">Code produit</Label>
                <Input
                  id="product_code"
                  name="product_code"
                  type="text"
                  value={formData.product_code}
                  onChange={handleChange}
                  placeholder="Code donné par le vendeur"
                  required
                  className="h-11"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="amount">Montant à payer (Ar)</Label>
                <Input
                  id="amount"
                  name="amount"
                  type="number"
                  value={formData.amount}
                  onChange={handleChange}
                  placeholder="0"
                  required
                  className="h-11"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="social_network">Réseau social utilisé</Label>
                <Select value={formData.social_network} onValueChange={(value) => setFormData({...formData, social_network: value})}>
                  <SelectTrigger className="h-11">
                    <SelectValue placeholder="Choisissez le réseau social" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Facebook">Facebook</SelectItem>
                    <SelectItem value="TikTok">TikTok</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm flex items-center">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  {error}
                </div>
              )}

              <Button 
                type="submit" 
                className="w-full h-11 bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700"
                disabled={loading}
              >
                {loading ? 'Création de la commande...' : 'Confirmer la commande'}
              </Button>
            </form>

            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-800">
                <strong>Important :</strong> Après validation, vous recevrez un numéro de commande. 
                Le paiement se fera via MVola selon les instructions du vendeur.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}