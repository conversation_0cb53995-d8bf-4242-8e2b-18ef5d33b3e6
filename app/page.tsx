import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Smartphone, Users, Shield, TrendingUp, CheckCircle, ArrowRight } from 'lucide-react';

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Smartphone className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-xl font-bold text-gray-900">Live Pay Mada</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/login">
                <Button variant="outline">Connexion</Button>
              </Link>
              <Link href="/register">
                <Button className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700">
                  S'inscrire
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <div className="flex items-center justify-center mb-8">
            <div className="bg-gradient-to-r from-blue-600 to-green-600 p-4 rounded-full">
              <Smartphone className="h-12 w-12 text-white" />
            </div>
          </div>
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Révolutionnez vos
            <span className="bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent"> ventes en live</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Fini les désistements ! Avec Live Pay Mada, vos clients paient directement pendant vos lives Facebook et TikTok via MVola.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/register">
              <Button size="lg" className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-lg px-8 py-3">
                Commencer maintenant
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link href="/login">
              <Button size="lg" variant="outline" className="text-lg px-8 py-3">
                Se connecter
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white/50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Pourquoi choisir Live Pay Mada ?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Une solution complète pour gérer vos ventes en live avec paiement sécurisé
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <div className="bg-blue-100 p-3 rounded-full w-fit">
                  <Smartphone className="h-6 w-6 text-blue-600" />
                </div>
                <CardTitle>Lien unique personnalisé</CardTitle>
                <CardDescription>
                  Obtenez un lien de commande unique à partager pendant vos lives
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <div className="bg-green-100 p-3 rounded-full w-fit">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <CardTitle>Paiement MVola intégré</CardTitle>
                <CardDescription>
                  Vos clients paient directement via MVola, plus de désistements
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <div className="bg-purple-100 p-3 rounded-full w-fit">
                  <TrendingUp className="h-6 w-6 text-purple-600" />
                </div>
                <CardTitle>Suivi des commandes</CardTitle>
                <CardDescription>
                  Tableau de bord complet avec export Excel pour vos analyses
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <div className="bg-orange-100 p-3 rounded-full w-fit">
                  <Users className="h-6 w-6 text-orange-600" />
                </div>
                <CardTitle>Multi-réseaux sociaux</CardTitle>
                <CardDescription>
                  Compatible avec Facebook Live et TikTok Live
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <div className="bg-red-100 p-3 rounded-full w-fit">
                  <Shield className="h-6 w-6 text-red-600" />
                </div>
                <CardTitle>Sécurisé et fiable</CardTitle>
                <CardDescription>
                  Plateforme sécurisée avec gestion automatique des numéros de commande
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <div className="bg-indigo-100 p-3 rounded-full w-fit">
                  <TrendingUp className="h-6 w-6 text-indigo-600" />
                </div>
                <CardTitle>Abonnement simple</CardTitle>
                <CardDescription>
                  50 000 Ar/mois, payable via MVola, activation immédiate
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* How it works */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Comment ça marche ?
            </h2>
            <p className="text-xl text-gray-600">
              En 3 étapes simples, commencez à vendre sans désistements
            </p>
          </div>

          <div className="space-y-12">
            <div className="flex flex-col md:flex-row items-center gap-8">
              <div className="bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">
                1
              </div>
              <div className="flex-1">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Inscrivez-vous</h3>
                <p className="text-gray-600">
                  Créez votre compte vendeur et configurez votre numéro MVola pour recevoir les paiements
                </p>
              </div>
            </div>

            <div className="flex flex-col md:flex-row items-center gap-8">
              <div className="bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">
                2
              </div>
              <div className="flex-1">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Partagez votre lien</h3>
                <p className="text-gray-600">
                  Obtenez votre lien unique et partagez-le pendant vos lives Facebook ou TikTok
                </p>
              </div>
            </div>

            <div className="flex flex-col md:flex-row items-center gap-8">
              <div className="bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">
                3
              </div>
              <div className="flex-1">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Recevez les commandes</h3>
                <p className="text-gray-600">
                  Vos clients commandent et paient directement, vous recevez tout dans votre tableau de bord
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-green-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Prêt à révolutionner vos ventes ?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Rejoignez les vendeurs qui ont déjà éliminé les désistements avec Live Pay Mada
          </p>
          <Link href="/register">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100 text-lg px-8 py-3">
              Commencer gratuitement
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
          <p className="text-blue-100 text-sm mt-4">
            Abonnement : 50 000 Ar/mois • Paiement via MVola
          </p>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center mb-4 md:mb-0">
              <Smartphone className="h-8 w-8 text-blue-400 mr-3" />
              <span className="text-xl font-bold">Live Pay Mada</span>
            </div>
            <div className="text-gray-400 text-sm">
              © 2025 Live Pay Mada. Tous droits réservés.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}