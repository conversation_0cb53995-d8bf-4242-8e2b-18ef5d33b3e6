'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Copy, ExternalLink, LogOut, Settings, Download, Smartphone } from 'lucide-react';

interface User {
  id: number;
  email: string;
  name: string;
  mvola_number?: string;
  subscription_start?: string;
  subscription_end?: string;
  is_active: boolean;
  unique_link: string;
}

interface Order {
  id: number;
  order_number: string;
  customer_name: string;
  customer_phone: string;
  delivery_address: string;
  product_code: string;
  amount: number;
  social_network: string;
  payment_status: string;
  created_at: string;
}

export default function DashboardPage() {
  const [user, setUser] = useState<User | null>(null);
  const [orders, setOrders] = useState<Order[]>([]);
  const [mvolaNumber, setMvolaNumber] = useState('');
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const router = useRouter();

  useEffect(() => {
    fetchUserProfile();
    fetchOrders();
  }, []);

  const fetchUserProfile = async () => {
    try {
      const response = await fetch('/api/vendor/profile');
      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
        setMvolaNumber(userData.mvola_number || '');
      } else {
        router.push('/login');
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchOrders = async () => {
    try {
      const response = await fetch('/api/vendor/orders');
      if (response.ok) {
        const ordersData = await response.json();
        setOrders(ordersData);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
    }
  };

  const updateProfile = async () => {
    setUpdating(true);
    try {
      const response = await fetch('/api/vendor/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ mvola_number: mvolaNumber }),
      });

      if (response.ok) {
        await fetchUserProfile();
        alert('Profil mis à jour avec succès');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
    } finally {
      setUpdating(false);
    }
  };

  const logout = async () => {
    await fetch('/api/auth/logout', { method: 'POST' });
    router.push('/login');
  };

  const copyLink = () => {
    if (user) {
      const link = `${window.location.origin}/order/${user.unique_link}`;
      navigator.clipboard.writeText(link);
      alert('Lien copié dans le presse-papiers !');
    }
  };

  const openOrderLink = () => {
    if (user) {
      const link = `${window.location.origin}/order/${user.unique_link}`;
      window.open(link, '_blank');
    }
  };

  const exportToExcel = () => {
    if (orders.length === 0) {
      alert('Aucune commande à exporter');
      return;
    }

    const csvContent = [
      ['Numéro', 'Client', 'Téléphone', 'Adresse', 'Code Produit', 'Montant', 'Réseau Social', 'Date'].join(','),
      ...orders.map(order => [
        order.order_number,
        order.customer_name,
        order.customer_phone,
        order.delivery_address,
        order.product_code,
        order.amount,
        order.social_network,
        new Date(order.created_at).toLocaleDateString('fr-FR')
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `commandes_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Smartphone className="h-12 w-12 text-blue-600 mx-auto mb-4 animate-pulse" />
          <p className="text-gray-600">Chargement...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  const isSubscriptionActive = user.is_active && user.subscription_end && new Date(user.subscription_end) > new Date();

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Smartphone className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">Live Pay Mada</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Bonjour, {user.name}</span>
              <Button variant="outline" size="sm" onClick={logout}>
                <LogOut className="h-4 w-4 mr-2" />
                Déconnexion
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Section */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Settings className="h-5 w-5 mr-2" />
                  Configuration
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="mvola">Numéro MVola</Label>
                  <Input
                    id="mvola"
                    value={mvolaNumber}
                    onChange={(e) => setMvolaNumber(e.target.value)}
                    placeholder="034 XX XXX XX"
                  />
                </div>
                <Button onClick={updateProfile} disabled={updating} className="w-full">
                  {updating ? 'Mise à jour...' : 'Mettre à jour'}
                </Button>

                <div className="pt-4 border-t">
                  <Label>Lien de commande</Label>
                  <div className="flex space-x-2 mt-2">
                    <Button variant="outline" size="sm" onClick={copyLink}>
                      <Copy className="h-4 w-4 mr-2" />
                      Copier
                    </Button>
                    <Button variant="outline" size="sm" onClick={openOrderLink}>
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Ouvrir
                    </Button>
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <div className={`p-3 rounded-md ${isSubscriptionActive ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                    <p className={`text-sm font-medium ${isSubscriptionActive ? 'text-green-800' : 'text-red-800'}`}>
                      Statut: {isSubscriptionActive ? 'Actif' : 'Inactif'}
                    </p>
                    {user.subscription_end && (
                      <p className={`text-xs mt-1 ${isSubscriptionActive ? 'text-green-600' : 'text-red-600'}`}>
                        Expire le: {new Date(user.subscription_end).toLocaleDateString('fr-FR')}
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Orders Section */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Commandes</CardTitle>
                    <CardDescription>
                      {orders.length} commande{orders.length !== 1 ? 's' : ''} au total
                    </CardDescription>
                  </div>
                  {orders.length > 0 && (
                    <Button variant="outline" size="sm" onClick={exportToExcel}>
                      <Download className="h-4 w-4 mr-2" />
                      Exporter Excel
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                {orders.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500">Aucune commande pour le moment</p>
                    <p className="text-sm text-gray-400 mt-2">
                      Partagez votre lien de commande pendant vos lives !
                    </p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>N°</TableHead>
                          <TableHead>Client</TableHead>
                          <TableHead>Produit</TableHead>
                          <TableHead>Montant</TableHead>
                          <TableHead>Réseau</TableHead>
                          <TableHead>Date</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {orders.map((order) => (
                          <TableRow key={order.id}>
                            <TableCell className="font-medium">{order.order_number}</TableCell>
                            <TableCell>
                              <div>
                                <p className="font-medium">{order.customer_name}</p>
                                <p className="text-sm text-gray-500">{order.customer_phone}</p>
                              </div>
                            </TableCell>
                            <TableCell>{order.product_code}</TableCell>
                            <TableCell className="font-medium">{order.amount.toLocaleString()} Ar</TableCell>
                            <TableCell>
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                order.social_network === 'Facebook' 
                                  ? 'bg-blue-100 text-blue-800' 
                                  : 'bg-pink-100 text-pink-800'
                              }`}>
                                {order.social_network}
                              </span>
                            </TableCell>
                            <TableCell className="text-sm text-gray-500">
                              {new Date(order.created_at).toLocaleDateString('fr-FR')}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}